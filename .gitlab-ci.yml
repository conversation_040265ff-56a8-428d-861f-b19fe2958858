# Cache modules using lock file
cache:
  key:
    files:
      - package-lock.json
  paths:
    - node_modules/

setup:
  only:
    - master
  script:
    - "docker run --rm -v `pwd`:/build -w /build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'npm ci && npm rebuild node-sass'"
    - "docker run --rm -v `pwd`:/build -w /build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'npm run build:prod;'"
    - ansible-playbook -l marc-aws-sw deploy/static.yml
    - ansible-playbook -l marc-aws-sw-stage deploy/static_stage.yml

setup_dev:
  only:
    - dev
  script:
    - "docker run --rm -v `pwd`:/build -w /build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'npm ci && npm rebuild node-sass'"
    - "docker run --rm -v `pwd`:/build -w /build -u `id -u $USER`:`id -g $USER` --env HOME=. itrdevsw/node-grunt:16 /bin/bash -c 'npm run build:dev'"
    - ansible-playbook -l marc-do-dev-sw-sw deploy/static_dev.yml
    - ansible-playbook -l marc-aws-sw-dev deploy/static_dev.yml
