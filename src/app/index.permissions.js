'use strict';

function permissionsFn (PermPermissionStore) {
    'ngInject';

    PermPermissionStore.definePermission('authenticated', authenticatedPermissionFn);
    PermPermissionStore.definePermission('hasEvent', eventPermissionFn);

    function authenticatedPermissionFn(permissionName, transitionProperties, mainService) {
        'ngInject';
        return mainService.isLogged();
    }

    function eventPermissionFn(permissionName, transitionProperties, mainService) {
        'ngInject';
        return mainService.hasPrevEvent();
    }

}

export default permissionsFn;