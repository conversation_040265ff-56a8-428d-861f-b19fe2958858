'use strict';

const shared = angular.module('core.shared', []);

import directives   from './directives/index';
import constants    from './services/constants';
import store        from './services/store.factory';
import resolver     from './services/resolver.provider';
import utcFilter    from './filters/utc.filter';


constants(shared);
store(shared);
directives(shared);
resolver(shared);
utcFilter(shared);

export default shared;
