'use strict';

import './main.scss';

import route        from './main.route';
import mainService  from './main.service';
import mainProvider from './main.provider';

import match_hotkeys_winner from './directives/match-hotkeys-winner.directive';
import matchInfo           from './directives/match-info.component';
import match_score          from './directives/match-scores-directive/match-scores.directive.js';

import MatchDataService     from './subpages/match/match-data.service';
import MatchStateService    from './subpages/match/match-state.service';
import SubmitResultsService from './subpages/match/submit/submit.service';

import confirmTimeComponent   from './subpages/match/confirm-time/confirm-time.component';
import scoreEntryComponent    from './subpages/match/score-entry/score-entry.component';
import submitResultsComponent from './subpages/match/submit/submit.component';
import winnerAndSetsComponent from './subpages/match/winner-and-sets/winner-and-sets.component';
import setCountComponent      from './subpages/match/winner-and-sets/sets-count/sets-count.component';
import winnerSetComponent     from './subpages/match/winner-and-sets/winner-set/winner-set.component';

import swapWinnerComponent    from './subpages/match/score-entry/swap-winner/swap-winner.component';
import confirmScoresComponent from './subpages/match/score-entry/confirm-scores/confirm-scores.component';
import ScoreEntryService      from './subpages/match/score-entry/score-entry.service';

import changeTimeComponent  from './subpages/match/confirm-time/change-time/change-time.component';

const mainPageModule = angular.module('main-module', [
  'ui.router'
]);

// Shared directives
mainPageModule.directive('matchHotkeysWinner', match_hotkeys_winner);
mainPageModule.directive('matchScores', match_score);

mainPageModule.service('mainService', mainService);
mainPageModule.service('MatchDataService', MatchDataService);
mainPageModule.service('MatchStateService', MatchStateService);
mainPageModule.service('ScoreEntryService', ScoreEntryService);
mainPageModule.service('SubmitResultsService', SubmitResultsService);
mainPageModule.provider('main', mainProvider);

mainPageModule.component('swapWinner', swapWinnerComponent);
mainPageModule.component('confirmScores', confirmScoresComponent);

mainPageModule.component('changeTime', changeTimeComponent);

mainPageModule.component('matchInfoHeading', matchInfo);
mainPageModule.component('matchConfirmTime', confirmTimeComponent);
mainPageModule.component('matchScoreEntry', scoreEntryComponent);
mainPageModule.component('matchSubmitResults', submitResultsComponent);
mainPageModule.component('matchWinnerAndSets', winnerAndSetsComponent);
mainPageModule.component('matchSetCount', setCountComponent);
mainPageModule.component('matchWinnerSet', winnerSetComponent);

mainPageModule.config(route);

export default mainPageModule;
