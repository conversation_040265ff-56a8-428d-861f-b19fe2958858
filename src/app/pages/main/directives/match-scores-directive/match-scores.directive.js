"use strict";

import { validationScores } from './scores-utils';

export default function matchScores($parse, $timeout) {
    'ngInject';

    return {
        restrict: "A",
        link: function ($scope, elem, attrs) {

            $timeout(function () {
                const _defaults = {
                    winnerName: 'WINNER',
                    inputSelector: '.form-control',
                    scopeObj: 'matchScores',
                    formControllerName: null,
                    is_tie_breaker: 0,
                    hide_winner: false,
                    tie_breaker_points: 25,
                    settings: {
                        "SetCount": 3,
                        "PlayAllSets": false,
                        "WinningPoints": 25,
                        "FinalPoints": 15,
                        "Duration": 60
                    }
                };

                const userOptions = $parse(attrs.matchScores)($scope);

                const options = angular.extend({}, _defaults, userOptions);

                const skipValidation = options.disableScoreEntryValidation;

                //Get input elements
                const $inputEls = elem.find(options.inputSelector);
                const formCtrl = $parse(options.formControllerName)($scope);

                // Bootstrap directive
                init();

                function init() {
                    $scope[options.scopeObj] = {
                        validationErrorMsg: '',
                        validationStatus: false
                    };

                    /* SW-736: allow to enter 3-digit scores for basketball events */
                    let maxLength = skipValidation ? 3 : 2; 
                    $inputEls.attr('maxlength', maxLength)

                    inputValidator();



                    // Set first focused
                    $inputEls.eq(0).trigger('click').select();

                    // Bind event listeners
                    bindEventListeners();

                    // Setup parsers for suitable input values
                    setInputsParsers();

                    $scope.$on('$destroy', function () {
                        unbindEventListeners($inputEls);
                    });
                }

                function bindEventListeners() {
                    addSpaceKeyListener(elem);
                    addInputChangeListener($inputEls);
                }

                function unbindEventListeners() {
                    elem.off();
                    $inputEls.off();
                }

                function setInputsParsers() {
                    for (var key in formCtrl) {
                        if (/team(\d)_set(\d)/.test(key) && formCtrl.hasOwnProperty(key)) {
                            addOnlyValuesParser(formCtrl[key]);
                        }
                    }

                    function addOnlyValuesParser(inputCtrl) {
                        inputCtrl.$parsers.push(function (value) {
                            if (value) {
                                var transformedInput = String(value).replace(/[^0-9]/g, '');
                                if (transformedInput !== value) {
                                    inputCtrl.$setViewValue(transformedInput);
                                    inputCtrl.$render();
                                }
                                return transformedInput;
                            }
                            return undefined;
                        });
                    }
                }

                function setErrorsMessage(validationRes) {
                    const default_message = '';

                    if (angular.isString(arguments[0])) {
                        return applyMsg(arguments[0]);
                    } else if (validationRes.isValid === false && validationRes.errors.length > 0) {
                        return applyMsg(validationRes.errors.map(e => e.msg));
                    } else {
                        return applyMsg(default_message);
                    }

                    function applyMsg(msg) {
                        $scope[options.scopeObj].validationErrorMsg = msg;
                    }
                }

                function setWarningsMessage(validationRes) {
                  const default_message = '';

                  if (angular.isString(arguments[0])) {
                      return applyMsg(arguments[0]);
                  } else if (validationRes.warnings && validationRes.warnings.length > 0) {
                      return applyMsg(validationRes.warnings.map(w => w.msg));
                  } else {
                      return applyMsg(default_message);
                  }

                  function applyMsg(msg) {
                      $scope[options.scopeObj].validationWarningsMsg = msg;
                  }
                }

                function setNextInputActive($input) {
                    var inputInd = $inputEls.index($input);
                    var nextInd = (inputInd === ($inputEls.length - 1) || inputInd === -1) ? 0 : (inputInd + 1);
                    $inputEls.eq(nextInd).trigger('click').select();
                }

                function addSpaceKeyListener(elem) {
                    elem.on('keyup', options.inputSelector, function (e) {
                        e.stopImmediatePropagation();
                        if (e.keyCode === 32) {
                            e.preventDefault();

                            var $inputEl = angular.element(e.currentTarget);
                            var teamInd = $inputEl.data('team');
                            var setInd = $inputEl.data('set');
                            var inputCtrlName = 'team' + teamInd + '_set' + setInd;
                            var inputCtrl = formCtrl[inputCtrlName];

                            if (options.settings.SetCount !== 1) {
                                var points = options.settings.MaxSetCount === setInd
                                    ? options.settings.FinalPoints
                                    : options.settings.WinningPoints;
                            } else {
                                var points = options.settings.WinningPoints;
                            }

                            inputCtrl.$setViewValue(points);
                            inputCtrl.$setValidity('maxlength', true); // check why maxlength validity still false ???
                            inputCtrl.$render();

                            setNextInputActive($inputEl);
                            inputValidator();
                        }
                    });
                }

                function addInputChangeListener($inputEls) {
                    $inputEls.on('keyup', inputValidator);
                }

                function inputValidator(e) {
                    const scoresInfo = formatScore($inputEls);
                    if (scoresInfo.availableKeys !== $inputEls.length) {
                        setErrorsMessage('');
                        setWarningsMessage('');
                        return;
                    }
                  
                    const scoresValidationRes = validationScores(scoresInfo.scores, options);

                    $scope.$applyAsync(function () {
                        if(!skipValidation) {

                            if(formCtrl) {
                                formCtrl.$setValidity('winner', scoresValidationRes.isValid);
                            }

                            $scope[options.scopeObj].validationStatus = scoresValidationRes.isValid;

                            setErrorsMessage(scoresValidationRes);
                            setWarningsMessage(scoresValidationRes);
                        }
                    });
                }

                function formatScore($inputEls) {
                    var scores = _.map($inputEls, function (input) {
                        return input.value;
                    });
                    var availableKeys = _.without(scores, '').length;

                    scores = scores.reduce(function (acc, item, index) {
                        if (index % 2 === 0) {
                            acc.push({});
                            acc[acc.length - 1].team1 = {
                                value: item,
                                index: index
                            };
                        } else {
                            acc[acc.length - 1].team2 = {
                                value: item,
                                index: index
                            };
                        }
                        return acc;
                    }, []);

                    return {
                        availableKeys: availableKeys,
                        scores: scores
                    };
                }

            });
        }
    };
}
