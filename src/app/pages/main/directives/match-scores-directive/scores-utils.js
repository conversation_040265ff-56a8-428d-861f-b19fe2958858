"use strict";

export function validationScores(scores, options) {
  let result    = validationSetScores(scores, options);

  if(!result.isValid) {
    return result;
  }

  if(options.settings.NoWinner || options.is_tie_breaker) {
    return { isValid: true, errors: [] };
  } else {
    result = validationTeamWinner(scores, options.winnerName, options.settings);

    if(!result.isValid && !options.settings.NoWinner) {
      return result;
    } else {
      return { isValid: true, errors: [] };
    }
  }

}

function getFinalPointsValue(matchIndex, matchesCount, options) {
  let settings = options.settings;

  if(options.is_tie_breaker && options.tie_breaker_points) {
    return options.tie_breaker_points;
  }

  if(settings.MaxSetCount === 5) {
    matchesCount = settings.MaxSetCount - 1;
  }

  let notFirstMatch = Number(matchIndex) !== 0;
  let evenMatch     = Number(matchIndex)%2 === 0;
  let lastMatch     = Number(matchIndex) === matchesCount;

  return notFirstMatch && evenMatch && lastMatch
      ? settings.FinalPoints
      : settings.WinningPoints;
}

export function validationSetScores(scores, options) {
  let arrayErrors = scores.reduce((errors, current, index, arr) => {
    const points  = getFinalPointsValue(index, (arr.length - 1), options);
    const diff    = Math.abs(current.team1.value - current.team2.value);
    const temp    = errors;

    if(diff === 0) {
      temp.push({id: 1, set: index+1, msg: `Set ${index+1}: Points cannot be equal.`});
    } else if(current.team1.value < points && current.team2.value < points) {
      temp.push({id: 2, set: index+1, msg: `Set ${index+1}: Winner points must be ${points} or more.`});
    } else if(diff !== 2 && (current.team1.value > points || current.team2.value > points)) {
      temp.push({id: 3, set: index+1, msg: `Set ${index+1} Warning: Winning team did not win by 2.`});
    } else if(diff === 1 && (Number(current.team1.value) === points || Number(current.team2.value) === points)) {
      temp.push({id: 4, set: index+1, msg: `Set ${index+1} Warning: Winning team did not win by 2.`});
    }

    return temp
  }, []);

  if( arrayErrors.length ) {
    return { isValid: false, errors: arrayErrors};
  } else {
    return { isValid: true, errors: [] };
  }
}

export function validationTeamWinner(scores, winnerName, settings) {
  const sets = scores.length;
  const isCheckVictoryInFinal = sets % 2;
  const expectedDiff = isCheckVictoryInFinal ? 1 : 2;
  let diff = 0, currentWinner = 0, winnerInLastSet = 0;
  let result = scores.reduce((result, set, index, arr) => {
    let key = parseInt(set.team1.value) > parseInt(set.team2.value) ? 'team1' : 'team2';
    result[key]++;
    return result;
  }, { team1: 0, team2: 0 } );

  currentWinner = result.team1 > result.team2 ? 1 : 2;
  diff = Math.abs(result.team1 - result.team2);

  if(isCheckVictoryInFinal) {
    let lastSetScores = _.last(scores);
    winnerInLastSet = parseInt(lastSetScores.team1.value) > parseInt(lastSetScores.team2.value) ? 1 : 2;
  }

  if(diff === 0 && !(settings.PlayAllSets && Number(settings.SetCount) === 2)) {
    return { isValid: false, errors: [{id: 4, msg: `Both teams won same number of sets.`}] };
  } else if(diff !== expectedDiff && !settings.PlayAllSets && !(settings.MaxSetCount === 5 && sets === 3)) {
      return {isValid: false, errors: [{id: 5, msg: `Winner should win at least ${parseInt(sets / 2) + 1} sets.`}]};
  } else if(currentWinner != '1' && !(settings.PlayAllSets && Number(settings.SetCount) === 2)) {
    return { isValid: false, errors: [{id: 6, msg: `Set ${sets}: Winner must be ${winnerName}.`}]};
  } else if(isCheckVictoryInFinal && currentWinner !== winnerInLastSet && !settings.PlayAllSets) {
    return { isValid: false, errors: [{id: 7, msg: `${winnerName} must win in set ${sets}!`}]};
  }

  return { isValid: true, errors: [] };
}
