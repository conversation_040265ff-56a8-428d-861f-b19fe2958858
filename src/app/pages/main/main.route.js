'use strict';

import searchTpl from './subpages/search/match-search.html';
import barcodeTpl from './subpages/barcode/match-barcode.html';

import matchBarcodeCtrl from './subpages/barcode/match-barcode.controller';
import matchSearchCtrl  from './subpages/search/match-search.controller';

function routeConfig($stateProvider, mainProvider) {
  'ngInject';

    $stateProvider.state('main', {
        abstract: true,
        parent: 'root',
        template: '<ui-view></ui-view>',
        data: {
            permissions: {
                only: ['hasEvent'],
                redirectTo: 'root.login'
            }
        }
    });

    $stateProvider.state('main.barcode', {
        url: 'event/:event',
        templateUrl: barcodeTpl,
        controller: matchBarcodeCtrl
    });

    $stateProvider.state('main.match-search', {
        url: 'event/:event/match-search',
        templateUrl: searchTpl,
        controller: matchSearchCtrl,
        resolve: {
            currentEvent: mainProvider.currentEvent
        }
    });

    $stateProvider.state('main.match', {
        url: 'event/:event/match/:match',
        abstract: true,
        template: '<ui-view></ui-view>',
        resolve: {
            currentEvent: mainProvider.currentEvent
        }
    });

    $stateProvider.state('main.match.winner-and-sets', {
        url: '/winner',
        parent: 'main.match',
        template: '<match-winner-and-sets current-event="$resolve.currentEvent"></match-winner-and-sets>',
        resolve: {
            checkMatchState: mainProvider.checkMatchState
        }
    });

    $stateProvider.state('main.match.score-entry', {
        url: '/scores',
        template: '<match-score-entry current-event="$resolve.currentEvent"></match-score-entry>',
        resolve: {
            checkMatchState: mainProvider.checkMatchState
        }
    });

    $stateProvider.state('main.match.confirm-time', {
        url: '/time',
        template: '<match-confirm-time current-event="$resolve.currentEvent"></match-confirm-time>',
        resolve: {
            checkMatchState: mainProvider.checkMatchState
        }
    });

    $stateProvider.state('main.match.submit-results', {
        url: '/results',
        template: '<match-submit-results current-event="$resolve.currentEvent"></match-submit-results>',
        resolve: {
            checkMatchState: mainProvider.checkMatchState
        }
    });
}

export default routeConfig;
