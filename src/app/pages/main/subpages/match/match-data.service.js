function MatchDataService($resource, URL) {
    'ngInject';

    this.$resource  = $resource;
    this.URL        = URL;
}

MatchDataService.prototype.getMatch = function ({event, barcode = 0, match = 0}) {
    let resultsResource = this.$resource(
        `${this.URL}/api/swr/check-match/:event/:barcode/:match`,
        {event:'@event', barcode: '@barcode', match:'@match'}
        );

    return resultsResource.get({ event, barcode, match }).$promise;
};

MatchDataService.prototype.submitMatchResults = function (results) {
    let resultsResource = this.$resource(`${this.URL}/api/swr/post-results`);

    return resultsResource.save(results).$promise;
};

export default MatchDataService;