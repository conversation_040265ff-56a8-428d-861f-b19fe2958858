import template from './confirm-scores.html';

function ConfirmScoresController ($scope) {
    'ngInject';

    this.warnings = $scope.$parent.matchScores.validationWarningsMsg;

    this.ok       = () => { this.close('ok') };
    this.cancel   = () => { this.close('cancel') };
}

export default {
    template    : template,
    controller  : ConfirmScoresController,
    bindings: {
        close   : '&',
        dismiss : '&'
    }
}