import template from './score-entry.html';

function scoreEntryController(ScoreEntryService, $state, MatchStateService, $scope, SubmitResultsService) {
    'ngInject';

    this.$onInit = function () {
        this.match      = MatchStateService.getMatchData();
        this.eventData  = this.currentEvent;

        ScoreEntryService.generateSets(this.match);

        let { team1, team2 } = ScoreEntryService.generateResults(this.match);

        this.team1 = team1;
        this.team2 = team2;

        $scope.matchScoresSettings = getMatchScoreSettings(this.match);
    };

    this.swapWinner = () => {
        ScoreEntryService.openSwapWinnerModal(this.match).then(match => this.match = match)
    };

    this.cancel = () => {
        let noWinnerSelection       = this.match.hide_winner;
        let noSetsCountSelection    = this.match.is_best_of_two;

        MatchStateService.clearMatchChanges();

        if (noWinnerSelection || noSetsCountSelection) {
            MatchStateService.gotoMainPage(this.eventData.has_match_barcodes, this.eventData.event_id);
        } else {
            $state.go('main.match.winner-and-sets');
        }
    };

    this.disableValidate = () => !this.match.team1_name || !this.match.team2_name;

    this.setActiveTeam = team => {
        this.activeTeam = team;
    };

    this.getWinnerName = function () {
        return Number(this.match.pre_winner) === 2 ? this.match.team2_name : this.match.team1_name
    };

    this.getLooserName = function () {
        return Number(this.match.pre_winner) === 2 ? this.match.team1_name : this.match.team2_name
    };

    this.submitResults = () => {
        const warnings = $scope.matchScores.validationWarningsMsg;

        if(angular.isArray(warnings) && warnings.length) {
            ScoreEntryService.openResultsConfirm().then(result => {
                if(result === 'ok') {
                    proceedNextStep();
                }
            });
        } else {
            proceedNextStep();
        }
    };

    this.showWinnerOptions = function () {
        return !this.match.hide_winner && !this.bestOfTwo;
    };

    const proceedNextStep = () => {
        this.match.result = {
            scores: {
                team1: this.team1.result,
                team2: this.team2.result
            }
        };

        MatchStateService.updateMatchData(this.match);

        if (this.eventData && Object.keys(this.eventData).length && !this.eventData.require_match_end_time) {
            $state.go('main.match.submit-results');
        } else {
            $state.go('main.match.confirm-time');
        }
    };

    function getMatchScoreSettings (match) {
        let userOptions = {
            MaxSetCount : match.settings.SetCount,
            SetCount    : match.set_count,
            FinalPoints : match.settings.FinalPoints,
        };

        return {
            inputSelector               : '.form-control',
            formControllerName          : '$ctrl.scores_form',
            scopeObj                    : 'matchScores',
            is_tie_breaker              : match.is_tie_breaker,
            hide_winner                 : match.hide_winner,
            tie_breaker_points          : match.tie_breaker_points,
            settings                    : angular.extend({}, match.settings, userOptions),
            disableScoreEntryValidation : match.disable_score_entry_validation,
            winnerName                  : match[`team${match.pre_winner}_name`]
        };
    }
}

export default {
    templateUrl : template,
    controller  : scoreEntryController,
    bindings    : {
        currentEvent: '<'
    }
};