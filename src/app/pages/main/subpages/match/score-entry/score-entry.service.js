function ScoreEntryService($uibModal, MatchStateService) {
    'ngInject';

    this.$uibModal          = $uibModal;
    this.MatchStateService  = MatchStateService;
}

ScoreEntryService.prototype.openResultsConfirm = function () {
    const confirmScores = this.$uibModal.open({ component: 'confirmScores' });

    return confirmScores.result;
};

ScoreEntryService.prototype.openSwapWinnerModal = function (match) {
    const swapModal = this.$uibModal.open({ component: 'swapWinner' });

    function changeWinner (winner) {
        return Number(winner) === 1 ? 2 : 1;
    }

    return swapModal.result.then(result => {
        if (result === 'swap') {
            match.pre_winner = changeWinner(match.pre_winner);

            if(match.results_data) {
                if(!_.isUndefined(match.results_data.winner)) {
                    match.results_data.winner = changeWinner(match.results_data.winner);
                } else {
                    match.results_data.winner = match.pre_winner;
                }
            } else {
                match.results_data = {
                    winner: match.pre_winner
                }
            }
        }

        return match;
    });
};

ScoreEntryService.prototype.generateSets = function (match) {
    let set_list = [];

    if (!match.set_count) {
        if(this.MatchStateService.isTieBreaker()) {
            match.set_count = 1;
        } else {
            match.set_count = match.settings.SetCount;
        }
    }

    for (let i = 0; i < match.set_count; i++) {
        set_list.push(i);
    }

    //???
    match.settings.SetList = set_list;
};

ScoreEntryService.prototype.generateResults = function (match) {
    let results = {
        team1: {
            name: "Team 1",
            result: {}
        },
        team2: {
            name: "Team 2",
            result: {}
        }
    };

    if (match && match.results_data && Object.keys(match.results_data).length) {
        //results already set by user
        if (match.result && match.result.scores && Object.keys(match.result.scores).length !== 0) {
            angular.forEach(match.result.scores, (result, team_name) => {
                angular.forEach(result, (res_data, set_name) => {
                    results[team_name].result[set_name] = res_data;
                });
            })
        } else {
            try {
                //results loaded from server
                angular.forEach(match.results_data, (val, prop) => {
                    if (prop.includes('set')) {
                        const values = val.match(/(\d+)-(\d+)/i);
                        if (angular.isArray(values)) {
                            results.team1.result[prop] = values[1];
                            results.team2.result[prop] = values[2];
                        }
                    }
                });
            } catch (e) {
                console.log('Unable to set scores:', e);
            }
        }
    }

    return results;
};

export default ScoreEntryService;