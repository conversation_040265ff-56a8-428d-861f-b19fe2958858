<form name="$ctrl.scores_form"
      ng-submit="$ctrl.submitResults($ctrl.scores_form)"
      match-scores="matchScoresSettings"
      novalidate
>
    <div class="match-team-box">
        <match-info-heading match="$ctrl.match"></match-info-heading>
        <table class="table vertical-text-alignment spacer-lg-b"
               ng-hide="$ctrl.disableValidate()">
            <colgroup>
                <col class="col-xs-3">
                <col class="col-xs-4">
                <col class="col-xs-4">
                <col class="col-xs-1">
            </colgroup>

            <tr class="team-row">
                <td colspan="4" class="winner-team" ng-class="{'success': $ctrl.activeTeam == 1}">
                    <strong ng-if="$ctrl.showWinnerOptions()">W:</strong>
                    <strong ng-bind="$ctrl.getWinnerName()"></strong>
                </td>
            </tr>
            <tr class="team-row">
                <td colspan="4" ng-class="{'success': $ctrl.activeTeam == 2}">
                    <strong ng-if="$ctrl.showWinnerOptions()">L:</strong>
                    <strong ng-bind="$ctrl.getLooserName()"></strong>
                </td>
            </tr>
            <tr class="team-row">
                <td></td>
                <td><b class="team-short-name" ng-bind="$ctrl.getWinnerName()"></b></td>
                <td><b class="team-short-name" ng-bind="$ctrl.getLooserName()"></b></td>
                <td></td>
            </tr>
            <tr class="score-row"
                ng-repeat="set in $ctrl.match.settings.SetList track by $index"
            >
                <td><b>Set {{$index+1}}</b></td>
                <td ng-class="{'success': $ctrl.activeTeam == 1}">
                    <input type="tel" name="team1_set{{$index+1}}"
                           ng-model="$ctrl.team1.result['set'+($index+1)]"
                           class="form-control"
                           ng-focus="$ctrl.setActiveTeam(1)"
                           id="team1_set{{$index+1}}"
                           data-team="1"
                           data-set="{{$index+1}}"
                           required
                           tabindex="{{$index+1}}">
                </td>
                <td ng-class="{'success': $ctrl.activeTeam == 2}">
                    <input type="tel" name="team2_set{{$index+1}}"
                           ng-model="$ctrl.team2.result['set'+($index+1)]"
                           class="form-control"
                           ng-focus="$ctrl.setActiveTeam(2)"
                           id="team2_set{{$index+1}}"
                           data-team="2"
                           data-set="{{$index+1}}"
                           required
                           tabindex="{{$index+2}}">
                </td>
                <td>&nbsp;</td>
            </tr>
        </table>
    </div>
    <div ng-show="matchScores.validationErrorMsg">
        <div ng-repeat="err in matchScores.validationErrorMsg track by $index"
             ng-bind="err"
             class="error-holder alert alert-danger">
        </div>
    </div>
    <div ng-show="matchScores.validationWarningsMsg">
        <div ng-repeat="warning in matchScores.validationWarningsMsg track by $index"
             ng-bind="warning"
             class="alert alert-warning">
        </div>
    </div>
    
    <div class="alert alert-warning" ng-if="$ctrl.disableValidate()" role="alert">
        Teams information is not ready for this match yet
    </div>
    
    <button type="submit" 
            class="btn btn-success btn-block submit-results-btn" 
            ng-hide="$ctrl.disableValidate()" 
            ng-disabled="$ctrl.scores_form.$invalid"
    >Validate</button>

    <button type="button" class="btn btn-block btn-danger submit-results-btn" ng-click="$ctrl.cancel()">Cancel</button>

    <button type="button"
            ng-if="$ctrl.showWinnerOptions()"
            class="btn btn-default btn-block"
            ng-click="$ctrl.swapWinner()"
    >Swap winner</button>
</form>
