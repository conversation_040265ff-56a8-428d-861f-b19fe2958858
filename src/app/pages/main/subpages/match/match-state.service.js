function MatchStateService ($rootScope, $state, toastr, MatchDataService, SW_SOURCE) {
    'ngInject';

    this.initialData    = null;
    this.matchData      = null;

    this.$rootScope     = $rootScope;
    this.$state         = $state;
    this.toastr         = toastr;

    this.MatchDataService = MatchDataService;

    this.SW_SOURCE = SW_SOURCE;
}

MatchStateService.prototype.goToInitialState = function (matchData, eventID) {
    this.setInitialData(matchData);
    this.updateMatchData(matchData);

    try {
        this.matchData.is_best_of_two
            = this.matchData.settings && this.matchData.settings.SetCount === 2 && this.matchData.settings.PlayAllSets;

        this.setDefaultSetsCount();

        this.$rootScope.isLiveEntry = this.matchData.score_entry_live;

        if (this.winnerAndSetsCanBeSkipped()) {
            this.$state.go('main.match.score-entry', { event: eventID, match: this.matchData.match_barcode });
        } else {
            this.$state.go('main.match.winner-and-sets', { event: eventID, match: this.matchData.match_barcode });
        }
    } catch (e) {
        console.error(e);
    }

};

MatchStateService.prototype.getMatchData = function () {
    return this.matchData;
};

MatchStateService.prototype.updateMatchData = function (matchData) {
    this.matchData = matchData;
};

MatchStateService.prototype.setInitialData = function (matchData) {
    this.initialData = Object.assign({}, matchData);
};

MatchStateService.prototype.clearMatchChanges = function () {
    this.matchData = Object.assign({}, this.initialData);
};

MatchStateService.prototype.winnerAndSetsCanBeSkipped = function () {
    return this.matchData.settings.SetCount === 2 && this.matchData.settings.PlayAllSets
};

MatchStateService.prototype.processBarcode = function ({event, match, barcode, loginSource}) {
    let params = {event, match, barcode};

    function formatResultsData(resultsData) {
        let data = resultsData;

        const lastCharPos = resultsData.length - 1;

        /// ????
        if (resultsData.charAt(0) === "'" && resultsData.charAt(lastCharPos) === "'") {
            data = resultsData.substring(1, lastCharPos);
        }
        try {
            return  angular.fromJson(data);
        } catch (e) {
            Sentry.captureException(e);
        }
    }

    return this.MatchDataService.getMatch(params).then(result => {
        let matchData = result;

        if (loginSource === this.SW_SOURCE) {
            matchData.redirectToSWAfterSumbit = true;
        }

        // matchData.settings for some events is in string format and need to be parsed
        if(_.isString(matchData.settings)) {
            try {
                matchData.settings = JSON.parse(matchData.settings);
            } catch (e) {
                console.error(e);
            }
        }

        if (!matchData || Object.keys(matchData).length === 0 || matchData.error || matchData.success === false) {
            this.toastr.error(matchData.message ? matchData.message : 'Response error. Please, try again');
            return;
        }

        if (matchData.results_data) {
            matchData.results_data = formatResultsData(matchData.results_data);
        }

        matchData.hide_winner = matchData.settings.NoWinner || matchData.hide_winner;

        this.goToInitialState(matchData, event);

        return matchData;
    })
};

MatchStateService.prototype.setDefaultSetsCount = function () {
    let MIN_SET         = 2;
    let MAX_SET         = 3;
    let defaultPoints   = 25;

    let finalPoints     = Number(this.matchData.settings.FinalPoints);
    let winningPoints   = Number(this.matchData.settings.WinningPoints);
    let setCount        = Number(this.matchData.settings.SetCount);
    let playAllSets     = this.matchData.settings.PlayAllSets;

    let default_set_list = [];

    //Temporary solution, need refactor
    if(this.matchData.settings.SetCount === 5) {
        MAX_SET = 5;
        MIN_SET = 3;
    }

    for (let i = MIN_SET; i <= MAX_SET; i++) {
        default_set_list.push({
            name: i,
            value: i
        });
    }

    if (finalPoints === defaultPoints) {
        let val = default_set_list[default_set_list.length-1].value;

        if (setCount === MAX_SET && winningPoints === defaultPoints && !playAllSets) {
            default_set_list.push({
                name: `${val} to ${defaultPoints}`,
                value: val
            });
        }
    }

    this.matchData.settings.DefaultSetList = default_set_list;
};

MatchStateService.prototype.hideWinnerSelection = function () {
    return this.matchData.settings.SetCount === 2 && this.matchData.settings.PlayAllSets && this.matchData.hide_winner;
};

MatchStateService.prototype.hideSetsCount = function () {
    return !_.isUndefined(this.__getSetType()) || this.matchData.settings.PlayAllSets;
};

MatchStateService.prototype.__getSetType = function () {
    try {
        // Return 1 set count if is_tie_breaker or SetCount == 1
        if (this.isTieBreaker() || Number(this.matchData.settings.SetCount === 1)) {
            return 1;
        }
    } catch (e) {
        // If error in match settings return undefined
        return undefined;
    }
    return undefined;
};

MatchStateService.prototype.isTieBreaker = function () {
    return Number(this.matchData.is_tie_breaker) === 1;
};

MatchStateService.prototype.gotoMainPage = function (hasBarcodes, eventID) {
    if (!hasBarcodes) {
        this.$state.go('main.match-search', {event: eventID});
    } else {
        this.$state.go('main.barcode', {event: eventID});
    }
};

export default MatchStateService;