<form name="winner_form" match-hotkeys-winner novalidate>
    <match-info-heading match="$ctrl.match"></match-info-heading>

    <match-winner-set
            ng-if="!$ctrl.hideWinnerSelection()"
            is-confirm-page=true
            team-one-name="$ctrl.match.team1_name"
            team-two-name="$ctrl.match.team2_name"
            update-winner="$ctrl.selectWinner(winner)"
    ></match-winner-set>

    <div>
        <button class="btn btn-block btn-success submit-results-btn"
                ng-disabled="$ctrl.disableSubmit()"
                ng-click="$ctrl.submitResults()"
        >Submit</button>
    </div>
    <div>
        <button class="btn btn-block btn-danger" ng-click="$ctrl.cancel()">Cancel</button>
    </div>
</form>
