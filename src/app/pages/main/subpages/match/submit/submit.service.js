function SubmitResultsService(MatchDataService, MatchStateService, mainService, $state, toastr) {
    'ngInject';

    this.MatchDataService   = MatchDataService;
    this.MatchStateService  = MatchStateService;
    this.mainService        = mainService;
    this.$state             = $state;
    this.toastr             = toastr;
}

SubmitResultsService.prototype.processSubmitting = function (match, eventData, winner) {
    let request = {
        event_id            : eventData.id,
        event_official_id   : eventData && eventData.event_official_id,
        match_barcode       : match.match_barcode,
        date_finished       : match.result ? match.result.secs_finished : "",
        score_entry_live    : match.score_entry_live
    };

    if(winner) {
        request.winner          = winner;
        request.winnning_team   = match[`team${winner}_name`];
    }

    request = this.formatSetResults(match, request);

    this.MatchDataService.submitMatchResults(request)
        .then(response => this.processSubmitResponse(response, eventData, match.redirectToSWAfterSumbit))
        .catch(() => this.toastr.error('Please, check login information and repeat'))
};

SubmitResultsService.prototype.processSubmitResponse = function (response, eventData, redirectToSWAfterSumbit) {
    if(response && !_.isUndefined(response.success) && !response.success) {
        if(response.message) {
            this.toastr.error(response.message);
        } else {
            this.toastr.error('Please, check login information and repeat');
        }

        return;
    }

    if (response.score_entry_live === false) {
        this.toastr.info(
            'Match Results submitted in DEMO mode and will NOT be recorded. ' +
            'Make sure you did not submit real match results in demo mode.', 'Warning!',
            {
                timeOut         : 30000,
                extendedTimeOut : 15000
            });
    } else {
        this.toastr.success('Result Successfully Saved!', 'Success!');
    }

    if (redirectToSWAfterSumbit) {
        this.mainService.redirectToSWOfficialSchedule();

        return;
    }

    this.processNextStep(eventData);
};

SubmitResultsService.prototype.processNextStep = function (eventData) {
    //logout after score entering has been completed for staffers
    if (eventData && eventData.event_official_id) {
        this.mainService.logout();
    } else {
        this.MatchStateService.gotoMainPage(eventData.has_match_barcodes, eventData.event_id);
    }
};

SubmitResultsService.prototype.formatSetResults = function (match, request) {
    try {
        let set_count   = match.set_count || match.settings.SetCount;
        let setIterator = 1;

        let resultScores = match.result.scores;

        for (let key in resultScores.team1) {
            if (resultScores.team1.hasOwnProperty(key) && setIterator <= set_count) {
                request[key] = `${resultScores.team1[key]}-${resultScores.team2[key]}`;
            } else {
                break;
            }

            setIterator++;
        }
    } catch (e) {
        console.log("Some set results unavailable");
    }

    return request;
};



export default SubmitResultsService;