import template from './submit.html';
import expiredModalTmpl from "_appRoot/pages/modals/expired/expired.modal.html";
import expiredModalCtrl from "_appRoot/pages/modals/expired/expired.modal.controller";

function submitResultsController(
    MatchStateService, $state, toastr, $uibModal, mainService, MatchDataService, SubmitResultsService
) {
    'ngInject';

    let winner;

    this.$onInit = function () {
        this.match      = MatchStateService.getMatchData();
        this.eventData  = this.currentEvent;

        this.isConfirmPage  = true;
    };

    this.selectWinner = function (winnerID) {
        winner = winnerID;
    };

    this.disableSubmit = function () {
        return this.match.is_best_of_two
            ? false
            : !winner
    };

    this.hideWinnerSelection = function () {
        return MatchStateService.hideWinnerSelection();
    };

    this.cancel = function () {
        $state.go('main.match.score-entry');
    };
    
    this.submitResults = () => {
        if (!this.match.is_best_of_two && Number(winner) !== Number(this.match.pre_winner)) {
            toastr.warning('Score mismatch. Please, check your results again!');

            $state.go('main.match.score-entry');

            return;
        }

        if (mainService.isLogged() !== true) {
            const expiredModal = $uibModal.open({
                templateUrl : expiredModalTmpl,
                controller  : expiredModalCtrl,
                backdrop    : 'static',
                resolve     : {
                    matchInfo: angular.copy(this.match)
                }
            });

            expiredModal.result
                .then(() => SubmitResultsService.processSubmitting(this.match, this.eventData, winner))
                .catch(() => mainService.logout());
        } else {
            SubmitResultsService.processSubmitting(this.match, this.eventData, winner);
        }
    };
}

export default {
    templateUrl : template,
    controller  : submitResultsController,
    bindings    : {
        currentEvent: '<'
    }
};