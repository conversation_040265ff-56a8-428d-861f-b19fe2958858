import template from './sets-count.html';

function setsCountController() {
    'ngInject';

    this.selectSetCount = function (set) {
        this.setsList.forEach(set => set.selected = false);
        set.selected = true;

        this.selectSet({set});
    };
}

export default {
    templateUrl : template,
    controller  : setsCountController,
    bindings: {
        setsList    : '<',
        selectSet   : '&'
    }
};