<form name="winner_form" match-hotkeys-winner novalidate>
    <match-info-heading match="$ctrl.match"></match-info-heading>

    <div class="alert alert-warning" ng-if="$ctrl.teamsDataNotFull" role="alert">
        Teams information is not ready for this match yet
    </div>

    <div ng-if="!$ctrl.teamsDataNotFull">

        <match-winner-set
                ng-if="!$ctrl.hideWinnerSelection()"
                team-one-name="$ctrl.match.team1_name"
                team-two-name="$ctrl.match.team2_name"
                update-winner="$ctrl.selectWinner(winner)"
                winner="$ctrl.match.results_data.winner"
        ></match-winner-set>

        <match-set-count
                ng-if="!$ctrl.hideSetsCount()"
                sets-list="$ctrl.match.settings.DefaultSetList"
                select-set="$ctrl.selectSetCount(set)"
        ></match-set-count>
    </div>
    <div>
        <button class="btn btn-block btn-danger" ng-click="$ctrl.cancel()">Cancel</button>
    </div>
</form>
