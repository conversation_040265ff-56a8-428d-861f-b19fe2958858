import template from './winner-set.html';

function winnerSetController(MatchStateService) {
    'ngInject';

    this.$onInit = function () {
        this.match = MatchStateService.getMatchData();
    };

    this.winnerSelected = function () {
        this.updateWinner({winner: this.winner})
    };

    this.hideWinnerSelection = function () {
        return MatchStateService.hideWinnerSelection();
    };
}

export default {
    templateUrl : template,
    controller  : winnerSetController,
    bindings: {
        isConfirmPage   : '@',
        teamOneName     : '<',
        teamTwoName     : '<',
        updateWinner    : '&',
        winner          : '<'
    }
};