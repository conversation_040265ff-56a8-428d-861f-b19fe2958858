import template from './winner-and-sets.html';

function winnerAndSetsController($stateParams, MatchStateService, $state) {
    'ngInject';

    let winner;
    let setCount;

    let DEFAULT_SET = {};

    this.$onInit = function () {
        this.match              = MatchStateService.getMatchData();
        this.eventData          = this.currentEvent;
        this.teamsDataNotFull   = teamsDataNotFull(this.match);

        if(this.match.results_data && this.match.results_data.winner) {
            winner = this.match.results_data.winner;
        }

        DEFAULT_SET = {
            name    : this.match.settings.SetCount,
            value   : this.match.settings.SetCount
        };
    };

    this.selectWinner = function (winnerID) {
        winner = winnerID;

        if(this.hideSetsCount()) {
            this.selectSetCount();
        }

        goToScoreEntry(this.match)
    };

    this.selectSetCount = (set) => {
        if (!set && this.hideSetsCount()) {
            set = DEFAULT_SET;
        }

        if(MatchStateService.isTieBreaker()) {
            setCount = 1;
        } else {
            setCount = set.value;
        }

        goToScoreEntry(this.match);
    };


    this.cancel = () => {
        MatchStateService.gotoMainPage(this.eventData.has_match_barcodes, $stateParams.event);
    };

    this.hideWinnerSelection = function () {
        return MatchStateService.hideWinnerSelection();
    };

    this.hideSetsCount = function () {
        return MatchStateService.hideSetsCount();
    };

    function teamsDataNotFull (match) {
        let fieldsThatShouldExist = ['team1_name', 'team2_name', 'team1_roster_id', 'team2_roster_id'];

        return fieldsThatShouldExist.some(field => !match[field]);
    }

    function goToScoreEntry(match) {
        if (winner && setCount) {
            match.pre_winner    = winner;
            match.set_count     = setCount;

            MatchStateService.updateMatchData(match);

            $state.go('main.match.score-entry');
        }
    }
}

export default {
    templateUrl : template,
    controller  : winnerAndSetsController,
    bindings: {
        currentEvent: '<'
    }
};