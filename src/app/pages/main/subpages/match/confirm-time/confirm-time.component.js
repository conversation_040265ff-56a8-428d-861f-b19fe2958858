import template from './confirm-time.html';

function confirmTimeController(mainService, $state, MatchStateService, MATCH_ENDED_TYPES, SubmitResultsService) {
    'ngInject';

    let changeMatchEndedTime    = false;
    let hasDateFinished         = false;

    this.$onInit = function () {
        this.match      = MatchStateService.getMatchData();
        this.eventData  = this.currentEvent;

        this.match.date_end = getMatchDateEnd(this.match);

        hasDateFinished = !!this.match.date_finished;

        if (hasDateFinished) {
            formatMatchFinalTimeType(this.match);
        }
    };

    function getMatchDateEnd (match) {
        let dateEndIsValid = moment(parseInt(match.date_end)).isValid();

        if (!match.date_end || !dateEndIsValid) {
            match.date_end = Number(match.date_start) + 60 * 60;
        }

        return match.date_end;
    }

    function formatMatchFinalTimeType (match) {
        const minutesDifference = getDatesDifferenceInMinutes(match.date_finished, match.date_end);

        const type = minutesDifference > 0
            ? MATCH_ENDED_TYPES.LATE.ID
            : (minutesDifference < 0
                ? MATCH_ENDED_TYPES.EARLY.ID
                : MATCH_ENDED_TYPES.ON_TIME.ID);

        const name = getTypeName(type);

        return  {
            min : Math.abs(minutesDifference),
            type: name.toUpperCase()
        };
    }

    function getDatesDifferenceInMinutes (start, end) {
        return Number((Number(start) - Number(end)) / 60);
    }

    function getTypeName (id) {
        const type = _.find(Object.values(MATCH_ENDED_TYPES), (type) => type.ID === id);
        return type && type.name;
    }

    this.disableInput = function () {
        return  !this.endedType ||
            (this.endedType.id !== MATCH_ENDED_TYPES.EARLY.ID && this.endedType.id !== MATCH_ENDED_TYPES.ON_TIME.ID);
    };

    this.changeMatchEnded = () => {
        changeMatchEndedTime   = true;
        this.endedType         = null;
    };

    this.submitResults = (matchEnded) => {
        if (!matchEnded) {
            this.match.result.secs_finished = this.match.date_finished;
        } else {
            this.match.result.secs_finished = matchEnded;
        }

        if(this.match.hide_winner) {
            SubmitResultsService.processSubmitting(this.match, this.eventData);
        } else {
            $state.go('main.match.submit-results');
        }
    };

    this.cancel = () => {
        this.endedType = null;

        $state.go('main.match.score-entry');
    };

    this.showCancelButton = function () {
        return !this.endedType || (hasDateFinished && !changeMatchEndedTime)
    };

    this.showChangeTime = function () {
        return !hasDateFinished || changeMatchEndedTime;
    };

    this.showBaseConfirmTimeOptions = function () {
        return hasDateFinished && !changeMatchEndedTime;
    }

}

export default {
    templateUrl : template,
    controller  : confirmTimeController,
    bindings    : {
        currentEvent: '<'
    }
};