import template from "./change-time.html";

function changeTimeController(MATCH_ENDED_TYPES) {
    'ngInject';

    let invalidDateRange = false;

    this.$onInit = function () {
        this.endedType  = null;
        this.matchEnded = null;

        this.minutes = {
            delta: null
        };

        this.minutesLabel = 'LATE';

        const currentMomentIsAfterMatchEnd = moment(this.recentTime)
            .clone()
            .add(10, 'minutes')
            .isAfter(this.dateEnd * 1000);

        this.formattedRecentTime    = moment(this.recentTime).tz(this.eventTimezone).format('h:mma');
        this.hideRecentlyBtn        = currentMomentIsAfterMatchEnd || this.dateFinished;
    };

    this.minutesChanged = function () {
        let minutes = this.minutes.delta * 60;
        let dateEnd = parseInt(this.dateEnd);
        
        if(this.endedType) {
            if (this.endedType.id === MATCH_ENDED_TYPES.EARLY.ID) {
                this.matchEnded = dateEnd - minutes;
                invalidDateRange = (this.minutes.delta < 0 || this.minutes.delta > 60);
            } else if (this.endedType.id === MATCH_ENDED_TYPES.LATE.ID) {
                this.matchEnded = dateEnd + minutes;
                invalidDateRange = false;
            }
        }
    };
    this.showMinutesInput = function () {
        return this.endedType &&
            (this.endedType.id === MATCH_ENDED_TYPES.EARLY.ID || this.endedType.id === MATCH_ENDED_TYPES.LATE.ID)
    };

    this.disableConfirm = () => !this.endedType || (this.endedType && (invalidDateRange || !this.minutes.delta));

    this.back = () => {
        this.endedType = null;
        this.minutes = {
            delta: null
        };
        invalidDateRange = false;
    };

    this.getTypeName = id => {
        const type = _.find(Object.values(MATCH_ENDED_TYPES), (type) => type.ID === id);
        return type && type.name;
    };

    this.submitResults = function () {
        this.submit({ matchEnded: this.matchEnded})
    };

    this.setMatchFinishedTime = time => this.match.result.secs_finished = time;

    this.setEndedTypeRecently = function () {
        this.updateEndedType(MATCH_ENDED_TYPES.RECENTLY.ID);

        this.matchEnded = moment(this.recentTime).format('X');
        this.submitResults();
    };

    this.setEndedTypeOnTime = function () {
        this.updateEndedType(MATCH_ENDED_TYPES.ON_TIME.ID);

        this.matchEnded = this.dateEnd;
        this.submitResults();
    };

    this.setEndedTypeEarly = function () {
        this.updateEndedType(MATCH_ENDED_TYPES.EARLY.ID);

        this.minutesLabel = 'EARLY';
    };

    this.setEndedTypeLate = function () {
        this.updateEndedType(MATCH_ENDED_TYPES.LATE.ID);

        this.minutesLabel = 'LATE';
    };

    this.updateEndedType = id => {
        this.endedType = { id, name: this.getTypeName(id) };
    };
}

export default {
    templateUrl : template,
    controller  : changeTimeController,
    bindings: {
        dateEnd         : '<',
        recentTime      : '<',
        dateFinished    : '<',
        eventTimezone   : '<',
        submit          : '&'
    }
};