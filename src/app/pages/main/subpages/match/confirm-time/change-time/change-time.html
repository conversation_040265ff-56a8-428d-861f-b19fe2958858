<form name="Form" novalidate class="match-time-form">
    <div ng-show="!$ctrl.endedType">
        <h3 class="text-center">This Match Ended
            <span ng-if="$ctrl.endedType.name" ng-bind="$ctrl.endedType.name"></span>
        </h3>
        <button class="btn btn-block btn-primary spacer-md-b"
                ng-hide="$ctrl.hideRecentlyBtn"
                ng-click="$ctrl.setEndedTypeRecently()">Recently {{($ctrl.formattedRecentTime)}}
        </button>
        <button class="btn btn-block btn-primary spacer-md-b" ng-click="$ctrl.setEndedTypeOnTime()">
            As Scheduled ({{($ctrl.dateEnd*1000) | UTCdate: 'h:mma'}} finish)
        </button>
        <button class="btn btn-block btn-success spacer-md-b" ng-click="$ctrl.setEndedTypeEarly()">Early</button>
        <button class="btn btn-block btn-danger spacer-md-b" ng-click="$ctrl.setEndedTypeLate()">Late</button>
    </div>

    <div ng-if="$ctrl.showMinutesInput()">
        <div class="text-center input-side-text">This Match Ended</div>

        <div class="col-xs-12">
            <div class="form-group">
                <div class="col-xs-5 text-right input-side-text">{{$ctrl.minutesLabel}} by</div>
                <div class="col-xs-4">
                    <input type="tel"
                           name="minutes_delta"
                           auto-focus
                           ng-model="$ctrl.minutes.delta"
                           ng-disabled="$ctrl.disableInput()"
                           class="form-control input-lg"
                           placeholder="Min"
                           ng-minlength="1"
                           ng-maxlength="2"
                           maxlength="2"
                           ng-change="$ctrl.minutesChanged()"
                           ng-class="{'error-input': Form.$invalid}"
                           validator="required, number, range"
                           max-time
                           valid-method="blur">
                </div>
                <div class="col-xs-3 text-left input-side-text">minutes</div>
            </div>
        </div>
    </div>

    <button ng-hide="!$ctrl.endedType"
            class="btn btn-block btn-success time-results-btn"
            ng-disabled="$ctrl.disableConfirm()"
            ng-click="$ctrl.submitResults()">
        Confirm Match Ended at <span ng-if="$ctrl.matchEnded">{{($ctrl.matchEnded*1000) | UTCdate: 'h:mma'}} </span>
    </button>

    <button type="button"
            class="btn btn-block btn-danger time-results-btn"
            ng-show="$ctrl.endedType"
            ng-click="$ctrl.back()"
    >Back</button>
</form>