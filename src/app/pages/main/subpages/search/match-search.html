<form name="step2_form">
  <h3 class="esw_title text-center">Match Search</h3>
  <div class="row">
    <div class="col-xs-10 col-xs-offset-1">
      <div class="clearfix spacer-lg-b">
        <label>Day</label>
        <div ng-repeat="(day_key , day) in days_array">
          <a 
             class="btn btn-default pull-left clip-link" 
             ng-class="{'btn-info': activeDay == $index}" 
             ng-click="getCourtsByDay(day, $index)">{{day | UTCdate:'ddd MM/DD'}}</a>
        </div>
      </div>

      <div class="clearfix spacer-lg-b">
        <label>Court</label>
        <select class="form-control spacer-mobile-sm-t" ng-disabled="!showCourts" ng-model="court" ng-change="getMatches(court)" ng-options="court.court_name for court in courts_array">
          <option value="">Choose court</option>
        </select>
        <div ng-if="courts_array && !courts_array.length" class="text-danger row-space">Event does not have matches/courts</div>
      </div>

      <div class="clearfix spacer-lg-b" ng-show="showMatches">
        <label>Matches</label>
        <div ng-repeat="match in matches_array" class="clearfix list-group-item match-search-item" ng-click="enterScores(match)" ng-class="getMatchClasses(match)">
          <div class="match-title">{{::match.date_start * 1000 | UTCdate:'hh:mm A'}}</div>
          <div class="match-name">{{::match.division_short_name}} {{::match.match_name}}</div>
          <div ng-if="!match.team_1_name">There's no teams yet</div>
          <div>
            <span ng-if="match.res_team1_roster_id == match.team1_roster_id && match.res_winner == 1">W:</span>
            <span ng-if="match.res_team2_roster_id == match.team2_roster_id && match.res_winner == 2">L:</span>
            <span>{{::match.team_1_name}}</span>
          </div>
          <div>
            <span ng-if="match.res_team1_roster_id == match.team1_roster_id && match.res_winner == 2">W:</span>
            <span ng-if="match.res_team2_roster_id == match.team2_roster_id && match.res_winner == 1">L:</span>
            <span>{{::match.team_2_name}}</span>
          </div>
          <div>

            <i ng-if="!showScores(match)" class="ref-team">{{::match.team_ref_name}}</i>
            <span ng-if="showScores(match)">{{ ::showScores(match) }}</span>
          </div>
        </div>
      </div>

      <div class="clearfix spacer-lg-b" ng-hide="true">
        <button type="submit" class="btn btn-primary btn-lg btn-block" ng-disabled="!match" validation-submit="step2_form" ng-click="enterScores()">Enter Scores</button>
      </div>
      <div class="clearfix">
        <button type="button" class="btn btn-info pull-right" ng-click="logout()">Logout</button>
        <button type="button" class="btn btn-info pull-right spacer-sm-r" ng-click="backToBarcode()">Enter Barcode</button>
      </div>
    </div>
  </div>
</form>
