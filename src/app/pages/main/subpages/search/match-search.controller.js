"use strict";

export default function matchSearchController($rootScope, $scope, $timeout, $state, toastr, mainService, currentEvent, MatchStateService) {
    'ngInject';

    const eventID = mainService.getLastEvent();

    $rootScope.matchSearchMode = true;
    $rootScope.matchSearchData = $rootScope.matchSearchData || {};

    const today = moment().utc().format('YYYY-MM-DD');

    $scope.match = null;

    $scope.courts_array     = [];
    $scope.showCourts       = false;
    $scope.matches_array    = [];
    $scope.showMatches      = false;

    $scope.getCourtsByDay   = (day, index) => loadCourts(day, index);
    $scope.backToBarcode    = () => $state.go('main.barcode', {event: eventID});
    $scope.showScores       = m => m.results && m.results.team1 && m.results.team1.scores;

    $scope.days_array = [];

    //format event days with matches
    if(currentEvent.match_days && currentEvent.match_days.length) {
        for (let i = 0, __date; i < currentEvent.match_days.length; i++) {
            __date = moment.utc(currentEvent.match_days[i], 'YYYY-MM-DD');

            if (currentEvent.match_days[i] === today) {
                loadCourts(__date, i);
            }
            $scope.days_array.push(__date);
        }
    }

    $scope.getMatches = court => {
        if (court) {
            $rootScope.matchSearchData.court = court;

            mainService.get_matches.get({
                event: eventID,
                day: $scope.event_day,
                court: court.court_id
            }).$promise.then(response => {
                $scope.matches_array = response.matches;

                $scope.matches_array.forEach(match => {
                    try {
                        match.results = angular.fromJson(match.results);
                    } catch (e) {}
                });

                $scope.showMatches = true;
            });
        } else {
            $scope.matches_array = [];
            $scope.showMatches = false;
        }
    };

    if (isOneDayEvent()) {
        $scope.getCourtsByDay($scope.days_array[0], 0);
    }

    if (!_.isEmpty($rootScope.matchSearchData) && !_.isNull($rootScope.matchSearchData.day)) {

        if (!$scope.activeDay) {
            $scope.activeDay = $rootScope.matchSearchData && $rootScope.matchSearchData.day;
            $scope.getCourtsByDay($scope.days_array[$rootScope.matchSearchData.day], $rootScope.matchSearchData.day);
        }

        $timeout(() => {
            $scope.court = $rootScope.matchSearchData && $rootScope.matchSearchData.court;

            if ($scope.court) {
                $scope.courts_array.forEach(court => {
                    if (angular.isObject(court) && court.court_id && court.court_id === $scope.court.court_id) {
                        $scope.court = court;
                    }
                });

                $scope.getMatches($scope.court);
            }
        }, 1000);
    }

    $scope.enterScores = match => {
        if (!match.team_1_name) {
            return;
        }

        MatchStateService.processBarcode({
            event: mainService.getLastEvent(),
            match: match.match_id
        });
    };

    $scope.getMatchClasses = ({results, is_tie_breaker}) => ({
        'match-has-scores'      : !_.isEmpty(results),
        'match-is-tie-breaker'  : is_tie_breaker === 1,
    });

    $scope.logout = () => {
        mainService.logout();
    };

    function isOneDayEvent () {
        return $scope.days_array.length === 1 && !$scope.activeDay;
    }

    function loadCourts (day, index) {
        $scope.showMatches  = false;
        $scope.match        = null;
        $scope.activeDay    = index;
        $scope.event_day    = moment(day).format('YYYY-MM-DD');

        $rootScope.matchSearchData.day = $scope.activeDay;

        mainService.get_courts.get({
            event   : eventID,
            day     : $scope.event_day
        }).$promise.then(response => {
            $scope.courts_array = response.courts;

            if (!$scope.courts_array.length) {
                toastr.error('Event does not have matches/courts.');
            }

            $scope.showCourts = true;
        });
    }
}
