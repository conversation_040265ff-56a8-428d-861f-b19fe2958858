"use strict";

export default function matchResultsBarcodeController($scope, $rootScope, $state, $cookies, $timeout, toastr, mainService, MatchStateService) {
    'ngInject';

    $rootScope.isLiveEntry = null;

    $scope.barcode = null;
    $scope.match = null;

    const resetTimer = scheduleResetEvent();

    if ($rootScope.enterResults && $rootScope.enterResults.event != undefined && $rootScope.enterResults.match != undefined) {
        $scope.match = $rootScope.enterResults.match;

        MatchStateService.processBarcode({
            event   : mainService.getLastEvent(),
            barcode : 0,
            match   : $scope.match
        });
    }

    const eventData = mainService.getEventData();
    $scope.showChooseBtn = eventData && eventData.show_all_matches;

    $scope.choose = () => {
        $rootScope.matchSearchData = {
            day: null,
            court: null,
            match: null
        };
        $state.go('main.match-search', {event: eventData.event_id});
    };

    $scope.$watch('barcode', (newVal, oldVal) => {
        if (newVal && newVal.length === 5) $scope.submit_barcode();
    });

    $scope.submit_barcode = () => {
        MatchStateService.processBarcode({
            event   : mainService.getLastEvent(),
            barcode : $scope.barcode
        });
    };

    $scope.logout = () => {
        mainService.logout();
    };

    $scope.$on('$destroy', (event) => {
        $timeout.cancel(resetTimer);

        let match = MatchStateService.getMatchData();
        $rootScope.isLiveEntry = match && match.score_entry_live;
    });

    function scheduleResetEvent () {
        const expiration = moment($cookies.get('auth_id'), 'x');
        if (expiration) {
            const remaining_time = expiration.diff(moment());

            console.log(`Session expires after: ${(remaining_time/1000/60).toFixed(2)} minutes`);

            return $timeout(() => {
                cancelSession();
            }, remaining_time);
        } else {
            cancelSession();
        }

        function cancelSession () {
            mainService.logout();
            toastr.info('Your session has been expired. Please, relogin.', 'Info!', {
                timeOut: 15000,
                extendedTimeOut: 15000
            });
        }

    }

}
