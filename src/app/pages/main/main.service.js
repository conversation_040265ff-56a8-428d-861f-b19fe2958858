export default function matchResultService(
    $rootScope, $http, $resource, $state, $stateParams, $cookies, $localStorage, toastr, URL, MatchStateService,
    SW_SOURCE, $location
) {
    'ngInject';

    let _event_data;

    try {
        _event_data = $localStorage.event_data || {};
    } catch (e) {
        _event_data = {};
    }

    const login_res          = $resource(`${URL}/api/swr/check-pin/:event_id/:pin`, {event_id:'@event_id', pin:'@pin'});
    const get_courts_res     = $resource(`${URL}/api/swr/get-event-courts/:event/:day`, {event:'@event', day:'@day'});
    const get_matches_res    =
        $resource(`${URL}/api/swr/get-matches/:event/:day/:court`, {event:'@event', day:'@day', court:'@court'});

    return {
        isLogged,
        hasPrevEvent,
        logout,
        loginHandler,
        checkDefaultPage,
        getLastEvent,
        getEventData,
        getEvent,

        login:          login_res,
        get_courts:     get_courts_res,
        get_matches:    get_matches_res,
        redirectToSWOfficialSchedule,
    };

    function isLogged () {
        const cookiesObject = $cookies.getAll();
        return !!(cookiesObject.auth_id && cookiesObject.last_result_event);
    }

    function hasPrevEvent () {
        const cookiesObject = $cookies.getAll();
        return angular.isDefined(cookiesObject.last_result_event);
    }

    function logout (preventRedirect) {
        _event_data = {};
        try {
            delete $localStorage.event_data;
        } catch (e) {}

        $cookies.remove('auth_id');
        $cookies.remove('last_result_event');
        $rootScope.isLiveEntry = null;
        if (preventRedirect !== true) {
            $state.go('root.login');
        }
    }

    function checkDefaultPage () {
        if (
            $state.is('root.login') &&
            $stateParams.username &&
            $stateParams.pin && isNaN(Number($stateParams.pin)) === false &&
            $stateParams.barcode && isNaN(Number($stateParams.barcode)) === false
        ) {
            const loginSource = SW_SOURCE;

            logout(true);
            login_res.get({event_id: $stateParams.username, pin: $stateParams.pin}).$promise
                .then(response => loginHandler(response, true))
                .then(() => MatchStateService.processBarcode({ event: getLastEvent(), barcode: $stateParams.barcode, loginSource }));
        } else if (isLogged() === true) {
            MatchStateService.gotoMainPage(_event_data.has_match_barcodes, getLastEvent());
        }

    }

    function getLastEvent () { return $cookies.get('last_result_event'); }

    function getEventData () { return angular.copy(_event_data); }

    function getEvent (id) {
        return $http({
            method: 'GET',
            url: `${URL}/api/esw/${id}`
        })
    }

    function loginHandler (response, preventRedirect) {
        if (response && (response.error || response.auth === false) ) {
            toastr.error(response.err || 'Login information is incorrect.');
            return;
        }
        const event_id = response.data ? response.data.event_id : $scope.username;

        const d = moment().add(3, 'hours');
        $cookies.put('auth_id', +d, {
            expires: d.toString()
        });
        $cookies.put('last_result_event', event_id);

        if (preventRedirect !== true ||
            ($rootScope.enterResults && $rootScope.enterResults.event != undefined && $rootScope.enterResults.match != undefined)) {

            MatchStateService.gotoMainPage(response.data.has_match_barcodes, event_id);
        }

        return response;
    }

    function redirectToSWOfficialSchedule() {
        const host = $location.host();
        const domain = host.includes('sportwrench') ? 'sportwrench' : 'swstage';

        const swUrl = NODE_ENV === 'production' 
            ? `https://my.${domain}.com/#`
            : `https://my.dev.${domain}.com/#`;

        window.location = `${swUrl}/official/schedule`;
    }

}
