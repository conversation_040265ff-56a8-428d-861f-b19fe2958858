'use strict';

export default function mainProvider() {
    'ngInject';

    this.checkMatchState  = checkMatchState;
    this.currentEvent     = getCurrentEvent;

    this.$get = function () {
        return this;
    };
}

function getCurrentEvent($rootScope, $stateParams, $q, $timeout, mainService) {
    'ngInject';

    var defer = $q.defer();

    mainService.getEvent($stateParams.event).then(function(response) {
        defer.resolve(response.data);
    });

    return defer.promise;
}

function checkMatchState (MatchStateService, $state, currentEvent) {
    'ngInject';

    let match = MatchStateService.getMatchData();

    if(_.isEmpty(match)) {
        MatchStateService.gotoMainPage(currentEvent.has_match_barcodes, currentEvent.event_id);
    }
}