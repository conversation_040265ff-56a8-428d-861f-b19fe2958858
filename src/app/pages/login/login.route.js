'use strict';

import loginTpl  from './login.html';
import loginCtrl from './login.controller';

function routeConfig($stateProvider) {
  'ngInject';

    $stateProvider.state('root.login', {
        url: '',
        templateUrl: loginTpl,
        controller: loginCtrl,
        params: {
            username: {
                value: ''
            },
            pin: {
                value: ''
            },
            barcode : {
                value: ''
            }
        }
    });

}

export default routeConfig;
