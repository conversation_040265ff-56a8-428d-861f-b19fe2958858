"use strict";

export default function matchResultsLoginController($rootScope, $scope, toastr, mainService) {
    'ngInject';

    mainService.checkDefaultPage();

    $scope.username = null;
    $scope.pin = null;

    if ($rootScope.enterResults && $rootScope.enterResults.event != undefined && $rootScope.enterResults.match != undefined) {
        $scope.username = $rootScope.enterResults.event;
    }

    $scope.login = function () {
        var showErrorMsg = function (err) {
            toastr.error(err || 'Login information is incorrect.');
        };
        mainService.login.get({event_id: $scope.username, pin: $scope.pin}).$promise
            .then(mainService.loginHandler, showErrorMsg);
    };

}
