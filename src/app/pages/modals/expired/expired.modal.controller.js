"use strict";

function expiredSessionModalCtrl ($scope, $q, $uibModalInstance, toastr, mainService, matchInfo, MatchStateService) {
    'ngInject';

    $scope.revalidateCredentials = function () {
        let loginErrorMsg = function (err) {
            toastr.error(err || 'Login information is incorrect.');
        };

        mainService.login.get({event_id: $scope.username, pin: $scope.pin}).$promise
            .then((response) => mainService.loginHandler(response, true), loginErrorMsg)
            .then(loginResponse => {
                if (loginResponse && loginResponse.auth) {
                    return MatchStateService.processBarcode({
                        event   : mainService.getLastEvent(),
                        barcode : $scope.barcode
                    });
                }
                return $q.reject();
            })
            .then((matchResponse) => {
                if (matchResponse.match_barcode === matchInfo.match_barcode) {
                    $uibModalInstance.close(matchResponse);
                } else {
                    toastr.info('Barcode mismatch with saved one.', 'Info!');
                }
            });
    };


}

export default expiredSessionModalCtrl;