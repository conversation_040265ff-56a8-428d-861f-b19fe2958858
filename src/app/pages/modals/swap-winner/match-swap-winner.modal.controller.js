"use strict";

export default function matchSwapWinnerModalCtrl ($scope, $uibModalInstance) {
    'ngInject';

    $scope.swap = function () {
        $uibModalInstance.close({
            type: 'swap',
            withResults: false
        });
    };

    $scope.swapWithResults = function () {
        $uibModalInstance.close({
            type: 'swap',
            withResults: true
        });
    };

    $scope.cancel = function () {
        $uibModalInstance.dismiss();
    };
}