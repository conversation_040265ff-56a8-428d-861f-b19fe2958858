'use strict';

import config       from './index.config';
import run          from './index.run';
import permissions  from './index.permissions';
import roles        from './index.roles';
import routes       from './index.routes';
import components   from './index.components';
import loginModule  from './pages/login/login.module';
import mainModule   from './pages/main/main.module';
import coreModule   from './core/core.module';


const App = angular.module(
  "sw-scores", [
    // plugins
    require('angular-ui-router'),
    "ngTouch", 
	"ngSanitize",
	"ngResource",
    "ngCookies",
	"oc.lazyLoad",
    "validation",
    "validation.rule",
	"ngStorage",
    "toastr",
    "ui.bootstrap",
    "permission",
    "permission.ui",


    // core
    coreModule.name,

    // components
    components.name,

    // routes
    routes.name,

    // pages
    loginModule.name,
    mainModule.name
  ]
);

App
  .config(config)
  .run(permissions)
  .run(roles)
  .run(run);



export default App;
