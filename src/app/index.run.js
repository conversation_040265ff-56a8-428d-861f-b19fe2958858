'use strict';

function runBlock($rootScope, $state, $http) {
	'ngInject';

	$rootScope.$on('$stateChangeError', function (e, to, toParams, from, fromParams, error) {
		console.dir({e, to, toParams, from, fromParams});
	});

	// oldLocation - previous state name
	$rootScope.$on('$stateChangeSuccess', function(event, toState, toParams, fromState) {
		$rootScope.oldLocation = fromState.name || $state.current.name;
	});

    fetchCsrfToken($http);
}

function fetchCsrfToken($http) {
  const methods = ['post', 'put', 'patch', 'delete'];
  $http.get('/api/csrfToken')
    .then(function ({_csrf}) {
      for(const method of methods) {
        if(!$http.defaults.headers[method]) {
          // DELETE method doesn't exist in $http.defaults.headers
          // https://code.angularjs.org/1.5.6/docs/api/ng/provider/$httpProvider
          $http.defaults.headers[method] = {
            'X-CSRF-Token': null
          };
        }

        $http.defaults.headers[method]['X-CSRF-Token'] = _csrf;
      }
    });
}

export default runBlock;
