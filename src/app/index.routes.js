'use strict';

import mainTpl  from './pages/main/main.html';
import mainCtrl from './pages/main/main.controller';

function routeConfig($urlRouterProvider, $stateProvider, resolverProvider) {
  'ngInject';

    $stateProvider
        .state('async', {
          url: '/async',
          templateUrl: require('!!file-loader?name=templates/[name].[ext]!./pages/async-page-example/async.html'),
          controller: 'asyncController',
          resolve: {
            asyncPreloading: resolverProvider.asyncPagePrealoading
          }
        });

    $stateProvider.state('root', {
        abstract: true,
        url: '/?username&pin&barcode',
        templateUrl: mainTpl,
        controller: mainCtrl
    });


  $urlRouterProvider.otherwise('/');

}

export default angular
  .module('index.routes', [])
    .config(routeConfig);

