'use strict';

function config($logProvider, $locationProvider, $qProvider, $validationProvider, $httpProvider, $uiViewScrollProvider) {
    'ngInject';

    $locationProvider.hashPrefix("!");

    $qProvider.errorOnUnhandledRejections(false);

    // Enable log
    $logProvider.debugEnabled(true);

    $validationProvider
        .setExpression({
            range: function (value, scope, element, attrs) {
                if (value >= parseInt(attrs.min) && value <= parseInt(attrs.max)) {
                    return value;
                }
            }
        })
        .setDefaultMsg({
            range: {
                error: '',
                success: 'good'
            }
        });

    $httpProvider.defaults.useXDomain = true;
    delete $httpProvider.defaults.headers.common['X-Requested-With'];

    $uiViewScrollProvider.useAnchorScroll();

}

export default config;
