(function() {
    angular.module('validation.rule', ['validation'])
        .config(['$validationProvider',
            function($validationProvider) {

                var expression = {
                    required: function(value) {
                        return !!value;
                    },
                    url: /^$|((([A-Za-z]{3,9}:(?:\/\/)?)(?:[-;:&=\+\$,\w]+@)?[A-Za-z0-9.-]+|(?:www.|[-;:&=\+\$,\w]+@)[A-Za-z0-9.-]+)((?:\/[\+~%\/.\w-_]*)?\??(?:[-\+=&;%@.\w_]*)#?(?:[\w]*))?)/,
                    email: /^\w+@\w+?\.[a-zA-Z]{2,3}$/,
                    number: /^\d+$/,
                    zip: /(^([0-9]{5})$)|(^[ABCEGHJKLMNPRSTVXYabceghjklmnprstvxy]{1}\d{1}[A-Za-z]{1} *\d{1}[A-Za-z]{1}\d{1}$)/,
                    reg_fee: /^([0-9]+[.][0-9])|([0-9][.][0-9]+)|([0-9]+)$/,
                    date: /^(?:(?:31(\/|-|\.)(?:0?[13578]|1[02]))\1|(?:(?:29|30)(\/|-|\.)(?:0?[1,3-9]|1[0-2])\2))(?:(?:1[6-9]|[2-9]\d)?\d{2})$|^(?:29(\/|-|\.)0?2\3(?:(?:(?:1[6-9]|[2-9]\d)?(?:0[48]|[2468][048]|[13579][26])|(?:(?:16|[2468][048]|[3579][26])00))))$|^(?:0?[1-9]|1\d|2[0-8])(\/|-|\.)(?:(?:0?[1-9])|(?:1[0-2]))\4(?:(?:1[6-9]|[2-9]\d)?\d{2})$/,
                    usav_memebrship: /[A-Z]{2}\d{5,7}[A-Z]{2,3}\d{2,3}/,
                    usav_director_memebership: function(value) {
                        if (!value) {
                            return true;
                        } else if (value.toUpperCase().match(/[A-Z]{2}\d{7}[A-Z]{2,3}\d{2,3}/)) {
                            return true;
                        } else {
                            return false;
                        }
                    },
                    jersey: function(value) {
                        if (!value) {
                            return true;
                        } else if (value.match(/^\d+$/)) {
                            return true;
                        } else {
                            return false;
                        }
                    },
                    phone: function(value) {
                        if (!value) {
                            return true;
                        } else if (value == '(___) ___-____') {
                            return true;
                        } else if (value.match(/^\(\d{3}\) \d{3}\-\d{4}$/)) {
                            return true;
                        } else {
                            return false;
                        }
                    },
                    text: function () { return true },
                    score: function(value) {
                        if (value > 40) return false;
                        return true;
                    }
                };

                var defaultMsg = {
                    required: {
                        error: 'This field is required',
                        success: ''
                    },
                    url: {
                        error: 'URL is incorrect. URL should start with http:// or https://',
                        success: ''
                    },
                    email: {
                        error: 'Email is required',
                        success: ''
                    },
                    number: {
                        error: 'Number is required',
                        success: ''
                    },
                    zip: {
                        error: 'Zip Code is invalid',
                        success: ''
                    },
                    reg_fee: {
                        error: 'Registration Fee incorrect',
                        success: ''
                    },
                    date: {
                        error: 'Date is incorrect',
                        success: ''
                    },
                    usav_memebrship: {
                        error: 'USAV code is incorrect',
                        success: ''
                    },
                    usav_director_memebership: {
                        error: 'USAV code is incorrect',
                        success: ''
                    },
                    phone: {
                        error: 'Phone is incorrect',
                        success: ''
                    },
                    jersey: {
                        error: 'Jersey is a number',
                        success: ''
                    },
                    score: {
                        error: 'Probably score is incorrect',
                        success: ''
                    },
                    text: {
                        
                    }
                };

                $validationProvider.setExpression(expression).setDefaultMsg(defaultMsg);
                $validationProvider.showSuccessMessage = false;
                $validationProvider.setErrorHTML(function (msg) {
                    return  "<span class=\"validation-invalid\">" + msg + "</span>";
                });
                $validationProvider.setSuccessHTML(function (msg) {
                    return  "<span class=\"validation-valid\">" + msg + "</span>";
                });

            }
        ]);

}).call(this);
