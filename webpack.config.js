'use strict';

const _ = require('lodash');

const _load = function() {
  const isBuild = process.env.NODE_ENV !== 'local';

  console.log(`Current Environment: ${process.env.NODE_ENV}`);

  return _.merge(
    require(__dirname + '/config/webpack/global')(__dirname, isBuild),
    require(`${__dirname}/config/webpack/environments/${process.env.NODE_ENV}`)(__dirname)
  );
};

module.exports = _load();
