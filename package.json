{"name": "sw-scores", "version": "0.1.0", "description": "Angular Webpack ES6 application generated by generator-angular-webpack-es6", "homepage": "", "scripts": {"start": "npm run-script dev", "dev": "cross-env NODE_ENV=local webpack-dev-server --progress --host 0.0.0.0 --config webpack.config.js", "build:dev": "cross-env NODE_ENV=development webpack -p --colors --config webpack.config.js", "build:prod": "cross-env NODE_ENV=production webpack -p --colors --config webpack.config.js"}, "dependencies": {"angular": "1.6.1", "angular-cookies": "1.6.1", "angular-permission": "^5.2.1", "angular-resource": "1.6.1", "angular-sanitize": "1.6.1", "angular-toastr": "^2.1.1", "angular-touch": "1.6.1", "angular-ui-bootstrap": "2.4.0", "angular-ui-router": "^0.4.3", "angular-validation": "^1.4.2", "assets-webpack-plugin": "^3.4.0", "autoprefixer": "^6.5.0", "babel-core": "^6.14.0", "babel-loader": "^7.1.4", "babel-plugin-angularjs-annotate": "^0.5.2", "babel-plugin-syntax-flow": "^6.13.0", "babel-plugin-transform-runtime": "^6.12.0", "babel-preset-es2015": "^6.16.0", "babel-preset-es2017": "^6.16.0", "babel-preset-stage-1": "^6.16.0", "babel-runtime": "^6.11.6", "baggage-loader": "^1.0.0", "bootstrap-sass": "^3.3.7", "clean-webpack-plugin": "^0.1.10", "cross-env": "^3.0.0", "css-loader": "^0.25.0", "es6-promise": "^4.0.3", "eslint-loader": "~1.6.0", "extract-text-webpack-plugin": "^3.0.2", "file-loader": "^0.9.0", "font-awesome": "^4.6.3", "html-loader": "^0.4.4", "html-webpack-plugin": "^2.22.0", "imagemin-pngquant": "^5.0.0", "jquery": "^3.1.1", "lodash": "^4.16.3", "manifest-revision-webpack-plugin": "^0.3.0", "moment": "^2.8.1", "moment-timezone": "^0.5.17", "ngstorage": "^0.3.11", "ngtemplate-loader": "^1.3.1", "node-sass": "^7.0.1", "oclazyload": "^1.0.9", "postcss-loader": "^0.13.0", "sass-loader": "^4.0.2", "style-loader": "^0.13.1", "url": "^0.11.0", "url-loader": "^0.5.7", "webpack": "^3.1.0", "webpack-dev-server": "^2.11.2"}, "engines": {"node": ">=0.12.0"}}