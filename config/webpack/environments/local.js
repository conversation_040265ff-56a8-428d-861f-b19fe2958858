'use strict';
const webpack = require('webpack');

module.exports = function(_path) {
  return {
    context: _path,
    devtool: 'cheap-source-map',
    devServer: {
      contentBase: './dist',
      hot: true,
      inline: true,
      proxy: {
        '/api': {
          target: 'https://scores-dev.swstage.com',
          secure: false,
          changeOrigin: true,
        }
      }
    },
    plugins: [
      new webpack.HotModuleReplacementPlugin()
    ]
  };
};
